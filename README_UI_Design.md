# HVAC08设备拓扑图UI界面设计说明

## 概述
根据您的建议，我重新设计了HVAC08设备拓扑图UI界面，主要改进如下：

### 1. 布局调整
- **上方区域**：简化的拓扑图，显示设备连接关系
- **下方区域**：详细的数据显示区域，使用标签页分类显示

### 2. 拓扑图部分（上方）
- 主控板HVAC08位于中央，包含IO板
- CAN-2总线横向连接所有设备
- 各设备以简化的方式显示，只显示设备名称和状态指示灯
- 设备包括：EXIO01-1/2、EXIO02-1/2、CBM01-1/2、CBM02-1/2

### 3. 数据显示部分（下方）
使用QTabWidget分为5个标签页：

#### 标签页1：HVAC08主控板
- **状态信息**：设备ID、开启状态、正常状态、各通信接口状态
- **版本信息**：固件版本、应用版本、引导版本、CPLD版本
- **电源信息**：5V电压、24V电压
- **网络信息**：IP地址、RTC时间、各RS485波特率

#### 标签页2：HVAC08-IO板
- **状态信息**：设备ID、开启状态、正常状态、CAN通信状态
- **版本信息**：固件版本、引导版本
- **电源信息**：5V电压、24V电压

#### 标签页3：EXIO设备
- **EXIO01设备**：EXIO01-1和EXIO01-2的详细信息
- **EXIO02设备**：EXIO02-1和EXIO02-2的详细信息
- 每个设备显示：ID、状态、版本、电压

#### 标签页4：CBM设备
- **CBM01设备**：CBM01-1和CBM01-2的详细信息
- **CBM02设备**：CBM02-1和CBM02-2的详细信息
- 每个设备显示：ID、状态、固件版本、硬件版本、AD版本、电压

#### 标签页5：MVB设备
- **MVB网卡信息**：设备ID、开启状态、各版本信息

## 报文数据映射

### DEVICE_INFO_RESPONSE结构中的所有字段都已映射到UI控件：

#### 主控板状态 (字节4-5)
- `hvac08_can` → LB_Hvac08_CAN
- `hvac08_485_0` → LB_Hvac08_485_0
- `hvac08_485_1` → LB_Hvac08_485_1
- `hvac08_485_2` → LB_Hvac08_485_2
- `hvac08_232` → LB_Hvac08_232
- `hvac08_eth` → LB_Hvac08_ETH
- `hvac08_mvb` → LB_Hvac08_MVB
- `hvac08_ad` → LB_Hvac08_AD
- `hvac08_io_can` → LB_Hvac08_IO_CAN

#### 各设备CAN状态 (字节6-9)
- `exio01_1_can` → LB_Exio01_1_Status
- `exio01_2_can` → LB_Exio01_2_Status
- `exio02_1_can` → LB_Exio02_1_Status
- `exio02_2_can` → LB_Exio02_2_Status
- `cbm01_1_can` → LB_Cbm01_1_Status
- `cbm01_2_can` → LB_Cbm01_2_Status
- `cbm02_1_can` → LB_Cbm02_1_Status
- `cbm02_2_can` → LB_Cbm02_2_Status

#### 主控板详细信息 (字节14-59)
- `hvac08_id` → LB_Hvac08_ID
- `hvac08_on` → LB_Hvac08_On
- `hvac08_nor` → LB_Hvac08_Nor
- `hvac08_fw_ver` → LB_Hvac08_FW_Ver
- `hvac08_app_ver` → LB_Hvac08_App_Ver
- `hvac08_boot_ver` → LB_Hvac08_Boot_Ver
- `hvac08_cpld_ver` → LB_Hvac08_CPLD_Ver
- `hvac08_5v` → LB_Hvac08_5V
- `hvac08_24v` → LB_Hvac08_24V
- `hvac08_rtc` → LB_Hvac08_RTC
- `hvac08_ip` → LB_Hvac08_IP
- `hvac08_rs485_0_baud` → LB_Hvac08_485_0_Baud
- `hvac08_rs485_1_baud` → LB_Hvac08_485_1_Baud
- `hvac08_rs485_2_baud` → LB_Hvac08_485_2_Baud

#### IO板信息 (字节60-75)
- `hvac08_io_id` → LB_Hvac08_IO_ID
- `hvac08_io_on` → LB_Hvac08_IO_On
- `hvac08_io_nor` → LB_Hvac08_IO_Nor
- `hvac08_io_fw_ver` → LB_Hvac08_IO_FW_Ver
- `hvac08_io_boot_ver` → LB_Hvac08_IO_Boot_Ver
- `hvac08_io_5v` → LB_Hvac08_IO_5V
- `hvac08_io_24v` → LB_Hvac08_IO_24V

#### MVB信息 (字节76-91)
- `mvb_id` → LB_MVB_ID
- `mvb_on` → LB_MVB_On
- `mvb_fw_ver` → LB_MVB_FW_Ver
- `mvb_app_ver` → LB_MVB_App_Ver
- `mvb_boot_ver` → LB_MVB_Boot_Ver

#### 各EXIO设备信息
每个EXIO设备都有对应的ID、状态、版本、电压显示控件

#### 各CBM设备信息
每个CBM设备都有对应的ID、状态、固件版本、硬件版本、AD版本、电压显示控件

## QSS样式特点

### 1. 设备状态样式
- **在线设备**：绿色边框和背景渐变
- **离线设备**：红色边框和背景渐变
- **未知状态**：灰色边框和背景渐变

### 2. 状态指示灯
- **在线**：绿色圆形指示灯
- **离线**：红色圆形指示灯
- **未知**：灰色圆形指示灯

### 3. 总线连接线
- **活跃总线**：绿色连接线
- **非活跃总线**：红色连接线

### 4. 整体风格
- 使用现代化的渐变背景
- 圆角边框设计
- 清晰的字体和颜色搭配

## 使用方法

### 1. 测试界面
```bash
python test_ui.py
```

### 2. 在实际项目中使用
```python
from PyQt5.uic import loadUi
loadUi('hvac08_device_full.ui', self)
```

### 3. 更新设备数据
通过控件名称直接更新对应的显示内容，例如：
```python
self.LB_Hvac08_ID.setText('1')
self.LB_Hvac08_On.setText('是')
self.LB_Hvac08_5V.setText('5.0V')
```

### 4. 更新设备状态
```python
# 设置设备在线状态
self.GB_Hvac08.setProperty('online', 1)
self.GB_Hvac08.style().unpolish(self.GB_Hvac08)
self.GB_Hvac08.style().polish(self.GB_Hvac08)

# 设置状态指示灯
self.LB_Hvac08_StatusLed.setProperty('statusLed', 'online')
self.LB_Hvac08_StatusLed.style().unpolish(self.LB_Hvac08_StatusLed)
self.LB_Hvac08_StatusLed.style().polish(self.LB_Hvac08_StatusLed)
```

## 文件说明
- `hvac08_device_full.ui` - 主UI文件
- `test_ui.py` - 测试程序
- `README_UI_Design.md` - 本说明文档

## 改进建议
1. 界面布局更加合理，拓扑图简洁明了
2. 数据显示完整，包含所有报文字段
3. 使用标签页分类，便于查看不同类型设备
4. QSS样式美观，状态显示直观
5. 易于扩展和维护
