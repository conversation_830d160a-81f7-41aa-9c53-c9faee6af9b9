#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的HVAC08设备拓扑图UI界面
"""

import sys
from PyQt5.QtWidgets import QApplication, QWidget, QMessageBox
from PyQt5.uic import loadUi

class TestWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        """初始化UI界面"""
        try:
            loadUi('hvac08_device_full.ui', self)
            self.setWindowTitle('HVAC08设备拓扑图测试 - 新版本')
            print("UI文件加载成功！")

            # 测试设置一些设备状态和数据
            self.test_device_status()
            self.test_device_data()

        except Exception as e:
            print(f"加载UI文件失败: {e}")
            QMessageBox.critical(self, "错误", f"加载UI文件失败: {e}")

    def test_device_status(self):
        """测试设置设备状态"""
        try:
            # 设置主控板在线状态
            if hasattr(self, 'GB_Hvac08'):
                self.GB_Hvac08.setProperty('online', 1)
                self.GB_Hvac08.style().unpolish(self.GB_Hvac08)
                self.GB_Hvac08.style().polish(self.GB_Hvac08)

            # 设置主控板状态指示灯
            if hasattr(self, 'LB_Hvac08_StatusLed'):
                self.LB_Hvac08_StatusLed.setProperty('statusLed', 'online')
                self.LB_Hvac08_StatusLed.style().unpolish(self.LB_Hvac08_StatusLed)
                self.LB_Hvac08_StatusLed.style().polish(self.LB_Hvac08_StatusLed)

            if hasattr(self, 'LB_Hvac08_StatusText'):
                self.LB_Hvac08_StatusText.setText('在线')

            # 设置一些EXIO设备为在线状态
            devices = ['GB_Exio01_1', 'GB_Exio02_1', 'GB_Cbm01_1']
            for device in devices:
                if hasattr(self, device):
                    gb = getattr(self, device)
                    gb.setProperty('online', 1)
                    gb.style().unpolish(gb)
                    gb.style().polish(gb)

            # 设置设备状态指示灯
            status_leds = ['LB_Exio01_1_StatusLed', 'LB_Exio02_1_StatusLed', 'LB_Cbm01_1_StatusLed']
            for led in status_leds:
                if hasattr(self, led):
                    led_widget = getattr(self, led)
                    led_widget.setProperty('statusLed', 'online')
                    led_widget.style().unpolish(led_widget)
                    led_widget.style().polish(led_widget)

            # 设置CAN总线为在线状态
            if hasattr(self, 'CAN2_Bus_Main'):
                self.CAN2_Bus_Main.setProperty('online', 1)
                self.CAN2_Bus_Main.style().unpolish(self.CAN2_Bus_Main)
                self.CAN2_Bus_Main.style().polish(self.CAN2_Bus_Main)

            if hasattr(self, 'HVAC08_to_CAN2'):
                self.HVAC08_to_CAN2.setProperty('online', 1)
                self.HVAC08_to_CAN2.style().unpolish(self.HVAC08_to_CAN2)
                self.HVAC08_to_CAN2.style().polish(self.HVAC08_to_CAN2)

            print("设备状态设置完成！")

        except Exception as e:
            print(f"设置设备状态时出错: {e}")

    def test_device_data(self):
        """测试设置设备数据"""
        try:
            # 设置HVAC08主控板数据
            if hasattr(self, 'LB_Hvac08_ID'):
                self.LB_Hvac08_ID.setText('1')
            if hasattr(self, 'LB_Hvac08_On'):
                self.LB_Hvac08_On.setText('是')
            if hasattr(self, 'LB_Hvac08_Nor'):
                self.LB_Hvac08_Nor.setText('是')
            if hasattr(self, 'LB_Hvac08_CAN'):
                self.LB_Hvac08_CAN.setText('连接')
            if hasattr(self, 'LB_Hvac08_485_0'):
                self.LB_Hvac08_485_0.setText('连接')
            if hasattr(self, 'LB_Hvac08_ETH'):
                self.LB_Hvac08_ETH.setText('连接')

            # 设置版本信息
            if hasattr(self, 'LB_Hvac08_FW_Ver'):
                self.LB_Hvac08_FW_Ver.setText('V2.1.0')
            if hasattr(self, 'LB_Hvac08_App_Ver'):
                self.LB_Hvac08_App_Ver.setText('V2.1.0')

            # 设置电源信息
            if hasattr(self, 'LB_Hvac08_5V'):
                self.LB_Hvac08_5V.setText('5.0V')
            if hasattr(self, 'LB_Hvac08_24V'):
                self.LB_Hvac08_24V.setText('24.0V')

            # 设置网络信息
            if hasattr(self, 'LB_Hvac08_IP'):
                self.LB_Hvac08_IP.setText('*************')
            if hasattr(self, 'LB_Hvac08_RTC'):
                self.LB_Hvac08_RTC.setText('2024-07-28 15:30:00')

            # 设置EXIO设备数据
            if hasattr(self, 'LB_Exio01_1_ID'):
                self.LB_Exio01_1_ID.setText('ID: 1')
            if hasattr(self, 'LB_Exio01_1_Status'):
                self.LB_Exio01_1_Status.setText('在线')
            if hasattr(self, 'LB_Exio01_1_Version'):
                self.LB_Exio01_1_Version.setText('V1.2.0')
            if hasattr(self, 'LB_Exio01_1_Voltage'):
                self.LB_Exio01_1_Voltage.setText('5V: 5.0V')

            # 设置CBM设备数据
            if hasattr(self, 'LB_Cbm01_1_ID'):
                self.LB_Cbm01_1_ID.setText('ID: 5')
            if hasattr(self, 'LB_Cbm01_1_Status'):
                self.LB_Cbm01_1_Status.setText('在线')
            if hasattr(self, 'LB_Cbm01_1_FW_Ver'):
                self.LB_Cbm01_1_FW_Ver.setText('固件: V1.3.0')
            if hasattr(self, 'LB_Cbm01_1_HW_Ver'):
                self.LB_Cbm01_1_HW_Ver.setText('硬件: V2.0.0')

            print("设备数据设置完成！")

        except Exception as e:
            print(f"设置设备数据时出错: {e}")

def main():
    app = QApplication(sys.argv)

    # 设置应用程序样式
    app.setStyle('Fusion')

    window = TestWindow()
    window.show()

    print("程序启动成功！")
    print("界面特点：")
    print("1. 上方是简化的拓扑图，显示设备连接关系")
    print("2. 下方是详细的数据显示区域，使用标签页分类显示")
    print("3. 包含所有报文结构中的数据字段")
    print("4. 使用QSS样式美化界面")

    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
