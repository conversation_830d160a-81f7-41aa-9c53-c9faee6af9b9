#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI文件是否可以正常加载
"""

import sys
from PyQt5.QtWidgets import QApplication, QWidget
from PyQt5.uic import loadUi

class TestWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """初始化UI界面"""
        try:
            loadUi('hvac08_device_full.ui', self)
            self.setWindowTitle('HVAC08设备拓扑图测试')
            print("UI文件加载成功！")
            
            # 测试设置一些设备状态
            self.test_device_status()
            
        except Exception as e:
            print(f"加载UI文件失败: {e}")
            
    def test_device_status(self):
        """测试设置设备状态"""
        try:
            # 设置主控板在线状态
            if hasattr(self, 'GB_Hvac08'):
                self.GB_Hvac08.setProperty('online', 1)
                self.GB_Hvac08.style().unpolish(self.GB_Hvac08)
                self.GB_Hvac08.style().polish(self.GB_Hvac08)
                
            # 设置一些设备状态指示灯
            if hasattr(self, 'LB_Hvac08_StatusLed'):
                self.LB_Hvac08_StatusLed.setProperty('statusLed', 'online')
                self.LB_Hvac08_StatusLed.style().unpolish(self.LB_Hvac08_StatusLed)
                self.LB_Hvac08_StatusLed.style().polish(self.LB_Hvac08_StatusLed)
                
            if hasattr(self, 'LB_Hvac08_StatusText'):
                self.LB_Hvac08_StatusText.setText('在线')
                
            # 设置版本信息
            if hasattr(self, 'LB_Hvac08_Version'):
                self.LB_Hvac08_Version.setText('固件版本: V2.1.0')
                
            # 设置一个EXIO设备为在线状态
            if hasattr(self, 'GB_Exio01_1'):
                self.GB_Exio01_1.setProperty('online', 1)
                self.GB_Exio01_1.style().unpolish(self.GB_Exio01_1)
                self.GB_Exio01_1.style().polish(self.GB_Exio01_1)
                
            if hasattr(self, 'LB_Exio01_1_StatusLed'):
                self.LB_Exio01_1_StatusLed.setProperty('statusLed', 'online')
                self.LB_Exio01_1_StatusLed.style().unpolish(self.LB_Exio01_1_StatusLed)
                self.LB_Exio01_1_StatusLed.style().polish(self.LB_Exio01_1_StatusLed)
                
            if hasattr(self, 'LB_Exio01_1_Status'):
                self.LB_Exio01_1_Status.setText('在线')
                
            # 设置CAN总线为在线状态
            if hasattr(self, 'CAN2_Bus_Main'):
                self.CAN2_Bus_Main.setProperty('online', 1)
                self.CAN2_Bus_Main.style().unpolish(self.CAN2_Bus_Main)
                self.CAN2_Bus_Main.style().polish(self.CAN2_Bus_Main)
                
            print("设备状态设置完成！")
            
        except Exception as e:
            print(f"设置设备状态时出错: {e}")

def main():
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
