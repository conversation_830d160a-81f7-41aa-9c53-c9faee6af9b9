<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Hvac08DeviceInfo</class>
 <widget class="QWidget" name="Hvac08DeviceInfo">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1228</width>
    <height>593</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">
QLabel{
	font-family: &quot;宋体&quot;; 
	font-size: 14px; 
}
QLabel[en=&quot;1&quot;]{
	min-width:20px;
	max-width:20px;
	min-height:20px;
	max-height:20px;
	border-radius:11px;
	background:green;
	border:1px solid #8B8989;                
}
QLabel[en=&quot;0&quot;]{
	min-width:20px;
	max-width:20px;
	min-height:20px;
	max-height:20px;
	border-radius:11px;
	background:white;
	border:1px solid #8B8989;;
}

QFrame[online=&quot;1&quot;]{
	border: 2px solid #228b22;                     
	background: #228b22;  
}
QFrame[online=&quot;0&quot;]{
	border: 2px solid #A0A0A0;
	background: #A0A0A0;
}

QGroupBox[online=&quot;1&quot;]{
	border: 4px solid #228b22;                
}
QGroupBox[online=&quot;0&quot;]{
	border: 4px solid #A0A0A0;
}

#LB_Hvac08_Ad{
	min-width:20px;
	max-width:20px;
	min-height:20px;
	max-height:20px;
	border-radius:11px;
	background:white;
	border:1px solid #8B8989;
}

QPushButton{
	border: 2px solid #7f8585; 
	border-radius: 3px; 	
	font-size: 14px;
	min-height: 28px;
	min-width: 100px;
	background-color: rgb(215, 215, 215)
}

QPushButton:hover {
	background-color: #4b6e8a; 
	color:#ffffff;
}

QGroupBox::title{
	subcontrol-origin: margin;
	subcontrol-position: top center;
	padding: 0 3px;
}

QGroupBox{
	border:4px solid #A0A0A0; 
	border-radius: 5px; 
	margin-top: 10px; 
	background: qlineargradient(x1:0, y1:0, x2:0, y2:1,stop:0 #f0f0f0, stop:1 #e0e0e0);                       
}

#GB_Hvac08_IO{
	border:2px dashed  #A0A0A0; 
}

QCheckBox{
	min-width:60px;
	min-height:20px;
	max-height:20px;	
}

QCheckBox::indicator::unchecked {	
	image: url(:/image/widget/checkbox_uncheck.png);
}

QCheckBox::indicator::checked { 
	image: url(:/image/widget/checkbox_checked.png);
}

#LB_RS485_0_EnState,
#LB_RS485_1_EnState,
#LB_RS485_2_EnState{
	min-width:24px;
	max-width:24px;
	min-height:24px;
	max-height:24px;
	border-radius:13px;
	background:white;
}</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_4">
   <item row="0" column="0">
    <widget class="QWidget" name="WDT_Topo" native="true">
     <property name="minimumSize">
      <size>
       <width>1150</width>
       <height>500</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>1150</width>
       <height>500</height>
      </size>
     </property>
     <widget class="QLabel" name="LB_RS232">
      <property name="geometry">
       <rect>
        <x>201</x>
        <y>220</y>
        <width>69</width>
        <height>25</height>
       </rect>
      </property>
      <property name="text">
       <string>RS485-1</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
      </property>
     </widget>
     <widget class="QGroupBox" name="GB_Group2">
      <property name="geometry">
       <rect>
        <x>21</x>
        <y>301</y>
        <width>150</width>
        <height>80</height>
       </rect>
      </property>
      <property name="title">
       <string/>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
      <layout class="QGridLayout" name="gridLayout_3">
       <item row="0" column="1">
        <widget class="QLabel" name="LB_Group2">
         <property name="text">
          <string>机组2</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="Line" name="LIE_CAN2_Exio02_1">
      <property name="geometry">
       <rect>
        <x>910</x>
        <y>270</y>
        <width>5</width>
        <height>36</height>
       </rect>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Plain</enum>
      </property>
      <property name="lineWidth">
       <number>5</number>
      </property>
      <property name="midLineWidth">
       <number>3</number>
      </property>
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
     </widget>
     <widget class="Line" name="LIE_RS485_2">
      <property name="geometry">
       <rect>
        <x>171</x>
        <y>343</y>
        <width>100</width>
        <height>5</height>
       </rect>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Plain</enum>
      </property>
      <property name="lineWidth">
       <number>5</number>
      </property>
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
     </widget>
     <widget class="QLabel" name="LB_RS485_1">
      <property name="geometry">
       <rect>
        <x>340</x>
        <y>160</y>
        <width>70</width>
        <height>25</height>
       </rect>
      </property>
      <property name="text">
       <string>RS485-0</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
      </property>
     </widget>
     <widget class="QGroupBox" name="GB_Group1">
      <property name="geometry">
       <rect>
        <x>21</x>
        <y>199</y>
        <width>150</width>
        <height>80</height>
       </rect>
      </property>
      <property name="title">
       <string/>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
      <layout class="QGridLayout" name="gridLayout_2">
       <item row="0" column="0">
        <widget class="QLabel" name="LB_Group1">
         <property name="text">
          <string>机组1</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QGroupBox" name="GB_Exio02_1">
      <property name="geometry">
       <rect>
        <x>830</x>
        <y>170</y>
        <width>150</width>
        <height>100</height>
       </rect>
      </property>
      <property name="title">
       <string/>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
      <layout class="QGridLayout" name="gridLayout_5">
       <item row="1" column="0">
        <widget class="QLabel" name="LB_Exio02_1_App">
         <property name="text">
          <string>版本: V1.0.0</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item row="0" column="0">
        <widget class="QLabel" name="LB_Exio02_1_Name">
         <property name="text">
          <string>EXIO02-1</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QGroupBox" name="GB_Hmi">
      <property name="geometry">
       <rect>
        <x>270</x>
        <y>20</y>
        <width>150</width>
        <height>100</height>
       </rect>
      </property>
      <property name="title">
       <string/>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
      <layout class="QGridLayout" name="gridLayout_9">
       <item row="0" column="0">
        <widget class="QLabel" name="LB_Hmi_Name">
         <property name="text">
          <string>HMI</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QGroupBox" name="GB_Mvb">
      <property name="geometry">
       <rect>
        <x>492</x>
        <y>20</y>
        <width>150</width>
        <height>100</height>
       </rect>
      </property>
      <property name="title">
       <string/>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
      <layout class="QGridLayout" name="gridLayout_8">
       <item row="0" column="0">
        <widget class="QLabel" name="LB_Mvb_Name">
         <property name="text">
          <string>MVB网卡</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="LB_Mvb_App">
         <property name="text">
          <string>版本: V1.0.0</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QLabel" name="LB_RS485_2">
      <property name="geometry">
       <rect>
        <x>200</x>
        <y>318</y>
        <width>70</width>
        <height>25</height>
       </rect>
      </property>
      <property name="text">
       <string>RS485-2</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
      </property>
     </widget>
     <widget class="QGroupBox" name="GB_Cbm02">
      <property name="geometry">
       <rect>
        <x>993</x>
        <y>170</y>
        <width>150</width>
        <height>100</height>
       </rect>
      </property>
      <property name="title">
       <string/>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
      <layout class="QGridLayout" name="gridLayout_7">
       <property name="leftMargin">
        <number>9</number>
       </property>
       <property name="topMargin">
        <number>9</number>
       </property>
       <property name="rightMargin">
        <number>9</number>
       </property>
       <item row="1" column="0">
        <widget class="QLabel" name="LB_Cbm02_App">
         <property name="text">
          <string>版本: V1.0.0</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item row="0" column="0">
        <widget class="QLabel" name="LB_Cbm02_Name">
         <property name="text">
          <string>CBM02-1</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="Line" name="LIE_CAN_2">
      <property name="geometry">
       <rect>
        <x>640</x>
        <y>306</y>
        <width>500</width>
        <height>6</height>
       </rect>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Plain</enum>
      </property>
      <property name="lineWidth">
       <number>5</number>
      </property>
      <property name="midLineWidth">
       <number>5</number>
      </property>
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
     </widget>
     <widget class="QLabel" name="LB_XT4">
      <property name="geometry">
       <rect>
        <x>579</x>
        <y>160</y>
        <width>69</width>
        <height>25</height>
       </rect>
      </property>
      <property name="text">
       <string>XT4</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
      </property>
     </widget>
     <widget class="Line" name="LIE_RS485_0">
      <property name="geometry">
       <rect>
        <x>171</x>
        <y>245</y>
        <width>100</width>
        <height>5</height>
       </rect>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Plain</enum>
      </property>
      <property name="lineWidth">
       <number>5</number>
      </property>
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
     </widget>
     <widget class="Line" name="LIE_CAN2_Cbm02_1">
      <property name="geometry">
       <rect>
        <x>1063</x>
        <y>270</y>
        <width>5</width>
        <height>36</height>
       </rect>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Plain</enum>
      </property>
      <property name="lineWidth">
       <number>5</number>
      </property>
      <property name="midLineWidth">
       <number>3</number>
      </property>
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
     </widget>
     <widget class="QLabel" name="LB_Can2">
      <property name="geometry">
       <rect>
        <x>645</x>
        <y>278</y>
        <width>121</width>
        <height>25</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>宋体</family>
        <pointsize>-1</pointsize>
       </font>
      </property>
      <property name="text">
       <string>CAN-2</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
      </property>
     </widget>
     <widget class="QGroupBox" name="GB_Exio02_2">
      <property name="geometry">
       <rect>
        <x>830</x>
        <y>337</y>
        <width>150</width>
        <height>100</height>
       </rect>
      </property>
      <property name="title">
       <string/>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
      <layout class="QGridLayout" name="gridLayout_6">
       <item row="1" column="0">
        <widget class="QLabel" name="LB_Exio02_2_App">
         <property name="text">
          <string>版本: V1.0.0</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item row="0" column="0">
        <widget class="QLabel" name="LB_Exio02_2_Name">
         <property name="text">
          <string>EXIO02-2</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="Line" name="LIE_XT4">
      <property name="geometry">
       <rect>
        <x>564</x>
        <y>119</y>
        <width>5</width>
        <height>70</height>
       </rect>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Minimum">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Plain</enum>
      </property>
      <property name="lineWidth">
       <number>5</number>
      </property>
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
     </widget>
     <widget class="QGroupBox" name="GB_Hvac08">
      <property name="geometry">
       <rect>
        <x>270</x>
        <y>179</y>
        <width>371</width>
        <height>261</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <property name="title">
       <string/>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
      <layout class="QGridLayout" name="gridLayout">
       <item row="2" column="0">
        <widget class="QLabel" name="LB_Hvac08_Merak_App">
         <property name="text">
          <string>校验值: V1.0.0</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item row="3" column="0">
        <widget class="QLabel" name="LB_RTC">
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="font">
          <font>
           <family>宋体</family>
           <pointsize>-1</pointsize>
          </font>
         </property>
         <property name="text">
          <string>2000-01-01 00:00:01</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item row="0" column="0">
        <widget class="QLabel" name="LB_Hvac08_Name">
         <property name="text">
          <string>扁平化控制器(HVAC08)</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="LB_Hvac08_App">
         <property name="text">
          <string>版本: V1.0.0</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item row="4" column="0">
        <widget class="QPushButton" name="PB_RTC_SYN">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>104</width>
           <height>32</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>30</height>
          </size>
         </property>
         <property name="text">
          <string>同步时钟</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1" rowspan="2">
        <widget class="QGroupBox" name="GB_Hvac08_IO">
         <property name="title">
          <string/>
         </property>
         <property name="online" stdset="0">
          <number>0</number>
         </property>
         <layout class="QGridLayout" name="gridLayout_11">
          <item row="1" column="0">
           <widget class="QLabel" name="LB_Hvac08_IO_App">
            <property name="text">
             <string>版本: V1.0.0</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item row="0" column="0">
           <widget class="QLabel" name="LB_Hvac08_IO_Name">
            <property name="text">
             <string>HVAC08-IO板</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="Line" name="LIE_RS485_1">
      <property name="geometry">
       <rect>
        <x>349</x>
        <y>119</y>
        <width>5</width>
        <height>70</height>
       </rect>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Minimum">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Plain</enum>
      </property>
      <property name="lineWidth">
       <number>5</number>
      </property>
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
     </widget>
     <widget class="Line" name="LIE_CAN2_Exio02_2">
      <property name="geometry">
       <rect>
        <x>910</x>
        <y>312</y>
        <width>5</width>
        <height>35</height>
       </rect>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Plain</enum>
      </property>
      <property name="lineWidth">
       <number>5</number>
      </property>
      <property name="midLineWidth">
       <number>3</number>
      </property>
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
     </widget>
     <widget class="Line" name="LIE_CAN2_Cbm02_2">
      <property name="geometry">
       <rect>
        <x>1063</x>
        <y>312</y>
        <width>5</width>
        <height>35</height>
       </rect>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Plain</enum>
      </property>
      <property name="lineWidth">
       <number>5</number>
      </property>
      <property name="midLineWidth">
       <number>3</number>
      </property>
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
     </widget>
     <widget class="QGroupBox" name="GB_Cbm02_2">
      <property name="geometry">
       <rect>
        <x>993</x>
        <y>337</y>
        <width>150</width>
        <height>100</height>
       </rect>
      </property>
      <property name="title">
       <string/>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
      <layout class="QGridLayout" name="gridLayout_10">
       <item row="1" column="0">
        <widget class="QLabel" name="LB_Cbm02_2_App">
         <property name="text">
          <string>版本: V1.0.0</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item row="0" column="0">
        <widget class="QLabel" name="LB_Cbm02_2_Name">
         <property name="text">
          <string>CBM02-2</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QCheckBox" name="CB_RS485_0_En">
      <property name="geometry">
       <rect>
        <x>384</x>
        <y>141</y>
        <width>60</width>
        <height>20</height>
       </rect>
      </property>
      <property name="text">
       <string>使能</string>
      </property>
      <property name="checked">
       <bool>true</bool>
      </property>
     </widget>
     <widget class="QCheckBox" name="CB_RS485_1_En">
      <property name="geometry">
       <rect>
        <x>203</x>
        <y>200</y>
        <width>71</width>
        <height>20</height>
       </rect>
      </property>
      <property name="text">
       <string>使能</string>
      </property>
      <property name="checked">
       <bool>true</bool>
      </property>
     </widget>
     <widget class="QCheckBox" name="CB_RS485_2_En">
      <property name="geometry">
       <rect>
        <x>203</x>
        <y>298</y>
        <width>60</width>
        <height>20</height>
       </rect>
      </property>
      <property name="text">
       <string>使能</string>
      </property>
      <property name="checked">
       <bool>true</bool>
      </property>
     </widget>
     <widget class="QLabel" name="LB_RS485_0_EnState">
      <property name="geometry">
       <rect>
        <x>356</x>
        <y>138</y>
        <width>26</width>
        <height>26</height>
       </rect>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="en" stdset="0">
       <number>0</number>
      </property>
     </widget>
     <widget class="QLabel" name="LB_RS485_1_EnState">
      <property name="geometry">
       <rect>
        <x>175</x>
        <y>198</y>
        <width>26</width>
        <height>26</height>
       </rect>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="en" stdset="0">
       <number>0</number>
      </property>
     </widget>
     <widget class="QLabel" name="LB_RS485_2_EnState">
      <property name="geometry">
       <rect>
        <x>175</x>
        <y>295</y>
        <width>26</width>
        <height>26</height>
       </rect>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="en" stdset="0">
       <number>0</number>
      </property>
     </widget>
     <zorder>GB_Cbm02_2</zorder>
     <zorder>LB_RS232</zorder>
     <zorder>GB_Group2</zorder>
     <zorder>LIE_CAN2_Exio02_1</zorder>
     <zorder>LIE_RS485_2</zorder>
     <zorder>LB_RS485_1</zorder>
     <zorder>GB_Group1</zorder>
     <zorder>GB_Exio02_1</zorder>
     <zorder>GB_Hmi</zorder>
     <zorder>GB_Mvb</zorder>
     <zorder>LB_RS485_2</zorder>
     <zorder>GB_Cbm02</zorder>
     <zorder>LB_XT4</zorder>
     <zorder>LIE_RS485_0</zorder>
     <zorder>LIE_CAN2_Cbm02_1</zorder>
     <zorder>LB_Can2</zorder>
     <zorder>GB_Exio02_2</zorder>
     <zorder>LIE_XT4</zorder>
     <zorder>GB_Hvac08</zorder>
     <zorder>LIE_RS485_1</zorder>
     <zorder>LIE_CAN2_Exio02_2</zorder>
     <zorder>LIE_CAN2_Cbm02_2</zorder>
     <zorder>LIE_CAN_2</zorder>
     <zorder>CB_RS485_0_En</zorder>
     <zorder>CB_RS485_1_En</zorder>
     <zorder>CB_RS485_2_En</zorder>
     <zorder>LB_RS485_0_EnState</zorder>
     <zorder>LB_RS485_1_EnState</zorder>
     <zorder>LB_RS485_2_EnState</zorder>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
