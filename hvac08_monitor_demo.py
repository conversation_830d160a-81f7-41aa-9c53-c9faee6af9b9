#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HVAC08设备监控演示程序
展示如何根据报文数据更新UI界面
"""

import sys
import struct
import random
from PyQt5.QtWidgets import QApplication, QWidget, QMessageBox
from PyQt5.QtCore import QTimer, pyqtSignal
from PyQt5.uic import loadUi

class DeviceInfoResponse:
    """设备信息响应数据结构"""
    def __init__(self):
        self.init_default()
    
    def init_default(self):
        """初始化默认值"""
        # 主控板状态
        self.hvac08_can = 0
        self.hvac08_on = 0
        self.hvac08_nor = 0
        self.hvac08_fw_ver = 0x01000000  # V1.0.0
        self.hvac08_ip = 0xC0A80164  # *************
        self.hvac08_5v = 5000  # 5.0V
        self.hvac08_24v = 24000  # 24.0V
        
        # IO板状态
        self.hvac08_io_can = 0
        self.hvac08_io_on = 0
        self.hvac08_io_nor = 0
        self.hvac08_io_fw_ver = 0x01000000
        
        # 各设备CAN状态
        self.exio01_1_can = 0
        self.exio01_1_on = 0
        self.exio01_1_fw_ver = 0x01000000
        self.exio01_1_5v = 5000
        
        self.exio01_2_can = 0
        self.exio01_2_on = 0
        self.exio01_2_fw_ver = 0x01000000
        self.exio01_2_5v = 5000
        
        self.exio02_1_can = 0
        self.exio02_1_on = 0
        self.exio02_1_fw_ver = 0x01000000
        self.exio02_1_5v = 5000
        
        self.exio02_2_can = 0
        self.exio02_2_on = 0
        self.exio02_2_fw_ver = 0x01000000
        self.exio02_2_5v = 5000
        
        self.cbm01_1_can = 0
        self.cbm01_1_on = 0
        self.cbm01_1_fw_ver = 0x01000000
        self.cbm01_1_5v = 5000
        
        self.cbm01_2_can = 0
        self.cbm01_2_on = 0
        self.cbm01_2_fw_ver = 0x01000000
        self.cbm01_2_5v = 5000
        
        self.cbm02_1_can = 0
        self.cbm02_1_on = 0
        self.cbm02_1_fw_ver = 0x01000000
        self.cbm02_1_5v = 5000
        
        self.cbm02_2_can = 0
        self.cbm02_2_on = 0
        self.cbm02_2_fw_ver = 0x01000000
        self.cbm02_2_5v = 5000

class HVAC08Monitor(QWidget):
    """HVAC08设备监控主窗口"""
    
    def __init__(self):
        super().__init__()
        self.device_info = DeviceInfoResponse()
        self.init_ui()
        self.init_timer()
        
    def init_ui(self):
        """初始化UI界面"""
        try:
            loadUi('hvac08_device_full.ui', self)
            self.setWindowTitle('HVAC08设备拓扑图监控演示')
            self.update_ui_display()
            print("UI界面初始化成功！")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载UI文件失败: {e}")
            sys.exit(1)
            
    def init_timer(self):
        """初始化定时器"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.simulate_data_update)
        self.timer.start(3000)  # 每3秒更新一次
        
    def simulate_data_update(self):
        """模拟数据更新"""
        # 模拟设备状态变化
        self.device_info.hvac08_on = random.choice([0, 1])
        self.device_info.hvac08_can = self.device_info.hvac08_on
        self.device_info.hvac08_io_on = random.choice([0, 1]) if self.device_info.hvac08_on else 0
        
        # 模拟各设备状态
        devices = [
            'exio01_1', 'exio01_2', 'exio02_1', 'exio02_2',
            'cbm01_1', 'cbm01_2', 'cbm02_1', 'cbm02_2'
        ]
        
        for device in devices:
            # 只有主控板在线时，其他设备才可能在线
            if self.device_info.hvac08_on:
                setattr(self.device_info, f'{device}_can', random.choice([0, 1]))
                setattr(self.device_info, f'{device}_on', getattr(self.device_info, f'{device}_can'))
            else:
                setattr(self.device_info, f'{device}_can', 0)
                setattr(self.device_info, f'{device}_on', 0)
            
            # 模拟电压变化
            if getattr(self.device_info, f'{device}_on'):
                setattr(self.device_info, f'{device}_5v', random.randint(4800, 5200))
            else:
                setattr(self.device_info, f'{device}_5v', 0)
        
        # 模拟主控板电压
        if self.device_info.hvac08_on:
            self.device_info.hvac08_5v = random.randint(4800, 5200)
            self.device_info.hvac08_24v = random.randint(23000, 25000)
        else:
            self.device_info.hvac08_5v = 0
            self.device_info.hvac08_24v = 0
        
        self.update_ui_display()
        
    def update_ui_display(self):
        """更新UI显示"""
        try:
            # 更新主控板状态
            self.update_device_status('GB_Hvac08', 'LB_Hvac08_StatusLed', 'LB_Hvac08_StatusText', 
                                    self.device_info.hvac08_on)
            
            # 更新版本信息
            if hasattr(self, 'LB_Hvac08_Version'):
                version = self.format_version(self.device_info.hvac08_fw_ver)
                self.LB_Hvac08_Version.setText(f'固件版本: {version}')
                
            # 更新IP地址
            if hasattr(self, 'LB_Hvac08_IP'):
                ip = self.format_ip(self.device_info.hvac08_ip)
                self.LB_Hvac08_IP.setText(f'IP地址: {ip}')
            
            # 更新IO板状态
            if hasattr(self, 'GB_Hvac08_IO'):
                self.update_groupbox_status('GB_Hvac08_IO', self.device_info.hvac08_io_on)
            if hasattr(self, 'LB_Hvac08_IO_StatusLed'):
                self.update_status_led('LB_Hvac08_IO_StatusLed', self.device_info.hvac08_io_on)
            
            # 更新各设备状态
            devices = [
                ('exio01_1', 'GB_Exio01_1', 'LB_Exio01_1_StatusLed', 'LB_Exio01_1_Status', 'LB_Exio01_1_Version', 'LB_Exio01_1_Voltage'),
                ('exio01_2', 'GB_Exio01_2', 'LB_Exio01_2_StatusLed', 'LB_Exio01_2_Status', 'LB_Exio01_2_Version', 'LB_Exio01_2_Voltage'),
                ('exio02_1', 'GB_Exio02_1', 'LB_Exio02_1_StatusLed', 'LB_Exio02_1_Status', 'LB_Exio02_1_Version', 'LB_Exio02_1_Voltage'),
                ('exio02_2', 'GB_Exio02_2', 'LB_Exio02_2_StatusLed', 'LB_Exio02_2_Status', 'LB_Exio02_2_Version', 'LB_Exio02_2_Voltage'),
                ('cbm01_1', 'GB_Cbm01_1', 'LB_Cbm01_1_StatusLed', 'LB_Cbm01_1_Status', 'LB_Cbm01_1_Version', 'LB_Cbm01_1_Voltage'),
                ('cbm01_2', 'GB_Cbm01_2', 'LB_Cbm01_2_StatusLed', 'LB_Cbm01_2_Status', 'LB_Cbm01_2_Version', 'LB_Cbm01_2_Voltage'),
                ('cbm02_1', 'GB_Cbm02_1', 'LB_Cbm02_1_StatusLed', 'LB_Cbm02_1_Status', 'LB_Cbm02_1_Version', 'LB_Cbm02_1_Voltage'),
                ('cbm02_2', 'GB_Cbm02_2', 'LB_Cbm02_2_StatusLed', 'LB_Cbm02_2_Status', 'LB_Cbm02_2_Version', 'LB_Cbm02_2_Voltage'),
            ]
            
            for device_name, gb_name, led_name, status_name, version_name, voltage_name in devices:
                device_on = getattr(self.device_info, f'{device_name}_on')
                self.update_device_status(gb_name, led_name, status_name, device_on)
                
                # 更新版本信息
                if hasattr(self, version_name):
                    version = self.format_version(getattr(self.device_info, f'{device_name}_fw_ver'))
                    getattr(self, version_name).setText(f'固件: {version}')
                
                # 更新电压信息
                if hasattr(self, voltage_name):
                    voltage = getattr(self.device_info, f'{device_name}_5v')
                    getattr(self, voltage_name).setText(f'5V: {voltage/1000:.1f}V')
            
            # 更新CAN总线状态
            can_online = any([
                self.device_info.hvac08_can,
                self.device_info.exio01_1_can, self.device_info.exio01_2_can,
                self.device_info.exio02_1_can, self.device_info.exio02_2_can,
                self.device_info.cbm01_1_can, self.device_info.cbm01_2_can,
                self.device_info.cbm02_1_can, self.device_info.cbm02_2_can
            ])
            
            if hasattr(self, 'CAN2_Bus_Main'):
                self.update_bus_status('CAN2_Bus_Main', can_online)
            if hasattr(self, 'HVAC08_to_CAN2'):
                self.update_bus_status('HVAC08_to_CAN2', can_online)
                
        except Exception as e:
            print(f"更新UI显示时出错: {e}")
    
    def update_device_status(self, gb_name, led_name, status_name, is_online):
        """更新设备状态"""
        self.update_groupbox_status(gb_name, is_online)
        self.update_status_led(led_name, is_online)
        self.update_status_text(status_name, is_online)
    
    def update_groupbox_status(self, gb_name, is_online):
        """更新GroupBox状态"""
        if hasattr(self, gb_name):
            gb = getattr(self, gb_name)
            gb.setProperty('online', 1 if is_online else 0)
            gb.style().unpolish(gb)
            gb.style().polish(gb)
    
    def update_status_led(self, led_name, is_online):
        """更新状态指示灯"""
        if hasattr(self, led_name):
            led = getattr(self, led_name)
            status = 'online' if is_online else 'offline'
            led.setProperty('statusLed', status)
            led.style().unpolish(led)
            led.style().polish(led)
    
    def update_status_text(self, status_name, is_online):
        """更新状态文本"""
        if hasattr(self, status_name):
            status_label = getattr(self, status_name)
            status_label.setText('在线' if is_online else '离线')
    
    def update_bus_status(self, bus_name, is_online):
        """更新总线状态"""
        if hasattr(self, bus_name):
            bus = getattr(self, bus_name)
            bus.setProperty('online', 1 if is_online else 0)
            bus.style().unpolish(bus)
            bus.style().polish(bus)
    
    def format_version(self, version_int):
        """格式化版本号"""
        major = (version_int >> 24) & 0xFF
        minor = (version_int >> 16) & 0xFF
        patch = (version_int >> 8) & 0xFF
        return f"V{major}.{minor}.{patch}"
    
    def format_ip(self, ip_int):
        """格式化IP地址"""
        return f"{ip_int & 0xFF}.{(ip_int >> 8) & 0xFF}.{(ip_int >> 16) & 0xFF}.{(ip_int >> 24) & 0xFF}"

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = HVAC08Monitor()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
