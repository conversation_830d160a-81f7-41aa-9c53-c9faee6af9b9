<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Hvac08DeviceFull</class>
 <widget class="QWidget" name="Hvac08DeviceFull">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1400</width>
    <height>800</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>HVAC08设备拓扑图</string>
  </property>
  <property name="styleSheet">
   <string notr="true">
/* 基础样式 */
QWidget {
    background-color: #f5f5f5;
    font-family: "Microsoft YaHei", "宋体";
    font-size: 12px;
}

/* 标题样式 */
QLabel#titleLabel {
    font-size: 18px;
    font-weight: bold;
    color: #2c3e50;
    background-color: #ecf0f1;
    border: 2px solid #bdc3c7;
    border-radius: 8px;
    padding: 10px;
    margin: 5px;
}

/* 设备组框样式 */
QGroupBox {
    border: 3px solid #95a5a6;
    border-radius: 8px;
    margin-top: 15px;
    padding-top: 10px;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:1 #ecf0f1);
    font-weight: bold;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 0 8px;
    color: #2c3e50;
    font-size: 14px;
}

/* 在线设备样式 */
QGroupBox[online="1"] {
    border: 3px solid #27ae60;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #d5f4e6, stop:1 #a8e6cf);
}

/* 离线设备样式 */
QGroupBox[online="0"] {
    border: 3px solid #e74c3c;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #fadbd8, stop:1 #f1948a);
}

/* 主控板特殊样式 */
QGroupBox#GB_Hvac08 {
    border: 4px solid #3498db;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ebf3fd, stop:1 #d6eaf8);
}

QGroupBox#GB_Hvac08[online="1"] {
    border: 4px solid #2980b9;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #d6eaf8, stop:1 #aed6f1);
}

/* IO板样式 */
QGroupBox#GB_Hvac08_IO {
    border: 2px dashed #7f8c8d;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f8f9fa, stop:1 #e9ecef);
}

/* 设备标签样式 */
QLabel {
    color: #2c3e50;
    font-size: 12px;
}

QLabel[deviceName="true"] {
    font-size: 14px;
    font-weight: bold;
    color: #2c3e50;
}

QLabel[version="true"] {
    font-size: 11px;
    color: #7f8c8d;
}

QLabel[status="true"] {
    font-size: 11px;
    color: #27ae60;
}

/* 状态指示灯样式 */
QLabel[statusLed="online"] {
    min-width: 16px;
    max-width: 16px;
    min-height: 16px;
    max-height: 16px;
    border-radius: 8px;
    background-color: #27ae60;
    border: 2px solid #1e8449;
}

QLabel[statusLed="offline"] {
    min-width: 16px;
    max-width: 16px;
    min-height: 16px;
    max-height: 16px;
    border-radius: 8px;
    background-color: #e74c3c;
    border: 2px solid #c0392b;
}

QLabel[statusLed="unknown"] {
    min-width: 16px;
    max-width: 16px;
    min-height: 16px;
    max-height: 16px;
    border-radius: 8px;
    background-color: #95a5a6;
    border: 2px solid #7f8c8d;
}

/* 连接线样式 */
QFrame[busLine="can"] {
    background-color: #3498db;
    border: 2px solid #2980b9;
}

QFrame[busLine="can"][online="1"] {
    background-color: #27ae60;
    border: 2px solid #1e8449;
}

QFrame[busLine="can"][online="0"] {
    background-color: #e74c3c;
    border: 2px solid #c0392b;
}

/* 总线标签样式 */
QLabel[busLabel="true"] {
    font-size: 13px;
    font-weight: bold;
    color: #2980b9;
    background-color: rgba(255, 255, 255, 200);
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    padding: 2px 6px;
}
   </string>
  </property>
  <layout class="QVBoxLayout" name="mainLayout">
   <item>
    <widget class="QLabel" name="titleLabel">
     <property name="text">
      <string>HVAC08设备拓扑图 - 实时状态监控</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="topologyWidget" native="true">
     <property name="minimumSize">
      <size>
       <width>1350</width>
       <height>700</height>
      </size>
     </property>

     <!-- 主控板 HVAC08 -->
     <widget class="QGroupBox" name="GB_Hvac08">
      <property name="geometry">
       <rect>
        <x>500</x>
        <y>250</y>
        <width>350</width>
        <height>200</height>
       </rect>
      </property>
      <property name="title">
       <string>HVAC08主控板</string>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
      <layout class="QVBoxLayout" name="hvac08Layout">
       <item>
        <widget class="QLabel" name="LB_Hvac08_Name">
         <property name="text">
          <string>扁平化控制器(HVAC08)</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
         <property name="deviceName" stdset="0">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="hvac08StatusLayout">
         <item>
          <widget class="QLabel" name="LB_Hvac08_Status">
           <property name="text">
            <string>状态:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="LB_Hvac08_StatusLed">
           <property name="text">
            <string></string>
           </property>
           <property name="statusLed" stdset="0">
            <string>unknown</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="LB_Hvac08_StatusText">
           <property name="text">
            <string>未知</string>
           </property>
           <property name="status" stdset="0">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QLabel" name="LB_Hvac08_Version">
         <property name="text">
          <string>固件版本: V1.0.0</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
         <property name="version" stdset="0">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="LB_Hvac08_IP">
         <property name="text">
          <string>IP地址: *************</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="GB_Hvac08_IO">
         <property name="title">
          <string>HVAC08-IO板</string>
         </property>
         <property name="online" stdset="0">
          <number>0</number>
         </property>
         <layout class="QHBoxLayout" name="ioLayout">
          <item>
           <widget class="QLabel" name="LB_Hvac08_IO_Status">
            <property name="text">
             <string>IO板状态:</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="LB_Hvac08_IO_StatusLed">
            <property name="text">
             <string></string>
            </property>
            <property name="statusLed" stdset="0">
             <string>unknown</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="LB_Hvac08_IO_Version">
            <property name="text">
             <string>V1.0.0</string>
            </property>
            <property name="version" stdset="0">
             <bool>true</bool>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>

     <!-- CAN-2总线 -->
     <widget class="QFrame" name="CAN2_Bus_Main">
      <property name="geometry">
       <rect>
        <x>200</x>
        <y>500</y>
        <width>950</width>
        <height>8</height>
       </rect>
      </property>
      <property name="frameShape">
       <enum>QFrame::Box</enum>
      </property>
      <property name="busLine" stdset="0">
       <string>can</string>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
     </widget>

     <!-- CAN-2总线标签 -->
     <widget class="QLabel" name="LB_CAN2_Label">
      <property name="geometry">
       <rect>
        <x>650</x>
        <y>475</y>
        <width>100</width>
        <height>25</height>
       </rect>
      </property>
      <property name="text">
       <string>CAN-2总线</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
      <property name="busLabel" stdset="0">
       <bool>true</bool>
      </property>
     </widget>

     <!-- 主控板到CAN总线的连接线 -->
     <widget class="QFrame" name="HVAC08_to_CAN2">
      <property name="geometry">
       <rect>
        <x>675</x>
        <y>450</y>
        <width>8</width>
        <height>50</height>
       </rect>
      </property>
      <property name="frameShape">
       <enum>QFrame::Box</enum>
      </property>
      <property name="busLine" stdset="0">
       <string>can</string>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
     </widget>

     <!-- EXIO01-1设备 -->
     <widget class="QGroupBox" name="GB_Exio01_1">
      <property name="geometry">
       <rect>
        <x>200</x>
        <y>550</y>
        <width>150</width>
        <height>120</height>
       </rect>
      </property>
      <property name="title">
       <string>EXIO01-1</string>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
      <layout class="QVBoxLayout" name="exio01_1_Layout">
       <item>
        <layout class="QHBoxLayout" name="exio01_1_StatusLayout">
         <item>
          <widget class="QLabel" name="LB_Exio01_1_StatusLed">
           <property name="text">
            <string></string>
           </property>
           <property name="statusLed" stdset="0">
            <string>unknown</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="LB_Exio01_1_Status">
           <property name="text">
            <string>离线</string>
           </property>
           <property name="status" stdset="0">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QLabel" name="LB_Exio01_1_Version">
         <property name="text">
          <string>固件: V1.0.0</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
         <property name="version" stdset="0">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="LB_Exio01_1_Voltage">
         <property name="text">
          <string>5V: 0.0V</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
      </layout>
     </widget>

     <!-- EXIO01-1连接线 -->
     <widget class="QFrame" name="Exio01_1_to_CAN2">
      <property name="geometry">
       <rect>
        <x>275</x>
        <y>508</y>
        <width>8</width>
        <height>42</height>
       </rect>
      </property>
      <property name="frameShape">
       <enum>QFrame::Box</enum>
      </property>
      <property name="busLine" stdset="0">
       <string>can</string>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
     </widget>

     <!-- EXIO01-2设备 -->
     <widget class="QGroupBox" name="GB_Exio01_2">
      <property name="geometry">
       <rect>
        <x>370</x>
        <y>550</y>
        <width>150</width>
        <height>120</height>
       </rect>
      </property>
      <property name="title">
       <string>EXIO01-2</string>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
      <layout class="QVBoxLayout" name="exio01_2_Layout">
       <item>
        <layout class="QHBoxLayout" name="exio01_2_StatusLayout">
         <item>
          <widget class="QLabel" name="LB_Exio01_2_StatusLed">
           <property name="text">
            <string></string>
           </property>
           <property name="statusLed" stdset="0">
            <string>unknown</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="LB_Exio01_2_Status">
           <property name="text">
            <string>离线</string>
           </property>
           <property name="status" stdset="0">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QLabel" name="LB_Exio01_2_Version">
         <property name="text">
          <string>固件: V1.0.0</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
         <property name="version" stdset="0">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="LB_Exio01_2_Voltage">
         <property name="text">
          <string>5V: 0.0V</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
      </layout>
     </widget>

     <!-- EXIO01-2连接线 -->
     <widget class="QFrame" name="Exio01_2_to_CAN2">
      <property name="geometry">
       <rect>
        <x>445</x>
        <y>508</y>
        <width>8</width>
        <height>42</height>
       </rect>
      </property>
      <property name="frameShape">
       <enum>QFrame::Box</enum>
      </property>
      <property name="busLine" stdset="0">
       <string>can</string>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
     </widget>

     <!-- EXIO02-1设备 -->
     <widget class="QGroupBox" name="GB_Exio02_1">
      <property name="geometry">
       <rect>
        <x>540</x>
        <y>550</y>
        <width>150</width>
        <height>120</height>
       </rect>
      </property>
      <property name="title">
       <string>EXIO02-1</string>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
      <layout class="QVBoxLayout" name="exio02_1_Layout">
       <item>
        <layout class="QHBoxLayout" name="exio02_1_StatusLayout">
         <item>
          <widget class="QLabel" name="LB_Exio02_1_StatusLed">
           <property name="text">
            <string></string>
           </property>
           <property name="statusLed" stdset="0">
            <string>unknown</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="LB_Exio02_1_Status">
           <property name="text">
            <string>离线</string>
           </property>
           <property name="status" stdset="0">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QLabel" name="LB_Exio02_1_Version">
         <property name="text">
          <string>固件: V1.0.0</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
         <property name="version" stdset="0">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="LB_Exio02_1_Voltage">
         <property name="text">
          <string>5V: 0.0V</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
      </layout>
     </widget>

     <!-- EXIO02-1连接线 -->
     <widget class="QFrame" name="Exio02_1_to_CAN2">
      <property name="geometry">
       <rect>
        <x>615</x>
        <y>508</y>
        <width>8</width>
        <height>42</height>
       </rect>
      </property>
      <property name="frameShape">
       <enum>QFrame::Box</enum>
      </property>
      <property name="busLine" stdset="0">
       <string>can</string>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
     </widget>

     <!-- EXIO02-2设备 -->
     <widget class="QGroupBox" name="GB_Exio02_2">
      <property name="geometry">
       <rect>
        <x>710</x>
        <y>550</y>
        <width>150</width>
        <height>120</height>
       </rect>
      </property>
      <property name="title">
       <string>EXIO02-2</string>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
      <layout class="QVBoxLayout" name="exio02_2_Layout">
       <item>
        <layout class="QHBoxLayout" name="exio02_2_StatusLayout">
         <item>
          <widget class="QLabel" name="LB_Exio02_2_StatusLed">
           <property name="text">
            <string></string>
           </property>
           <property name="statusLed" stdset="0">
            <string>unknown</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="LB_Exio02_2_Status">
           <property name="text">
            <string>离线</string>
           </property>
           <property name="status" stdset="0">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QLabel" name="LB_Exio02_2_Version">
         <property name="text">
          <string>固件: V1.0.0</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
         <property name="version" stdset="0">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="LB_Exio02_2_Voltage">
         <property name="text">
          <string>5V: 0.0V</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
      </layout>
     </widget>

     <!-- EXIO02-2连接线 -->
     <widget class="QFrame" name="Exio02_2_to_CAN2">
      <property name="geometry">
       <rect>
        <x>785</x>
        <y>508</y>
        <width>8</width>
        <height>42</height>
       </rect>
      </property>
      <property name="frameShape">
       <enum>QFrame::Box</enum>
      </property>
      <property name="busLine" stdset="0">
       <string>can</string>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
     </widget>

     <!-- CBM01-1设备 -->
     <widget class="QGroupBox" name="GB_Cbm01_1">
      <property name="geometry">
       <rect>
        <x>880</x>
        <y>550</y>
        <width>150</width>
        <height>120</height>
       </rect>
      </property>
      <property name="title">
       <string>CBM01-1</string>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
      <layout class="QVBoxLayout" name="cbm01_1_Layout">
       <item>
        <layout class="QHBoxLayout" name="cbm01_1_StatusLayout">
         <item>
          <widget class="QLabel" name="LB_Cbm01_1_StatusLed">
           <property name="text">
            <string></string>
           </property>
           <property name="statusLed" stdset="0">
            <string>unknown</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="LB_Cbm01_1_Status">
           <property name="text">
            <string>离线</string>
           </property>
           <property name="status" stdset="0">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QLabel" name="LB_Cbm01_1_Version">
         <property name="text">
          <string>固件: V1.0.0</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
         <property name="version" stdset="0">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="LB_Cbm01_1_Voltage">
         <property name="text">
          <string>5V: 0.0V</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
      </layout>
     </widget>

     <!-- CBM01-1连接线 -->
     <widget class="QFrame" name="Cbm01_1_to_CAN2">
      <property name="geometry">
       <rect>
        <x>955</x>
        <y>508</y>
        <width>8</width>
        <height>42</height>
       </rect>
      </property>
      <property name="frameShape">
       <enum>QFrame::Box</enum>
      </property>
      <property name="busLine" stdset="0">
       <string>can</string>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
     </widget>

     <!-- CBM01-2设备 -->
     <widget class="QGroupBox" name="GB_Cbm01_2">
      <property name="geometry">
       <rect>
        <x>1050</x>
        <y>550</y>
        <width>150</width>
        <height>120</height>
       </rect>
      </property>
      <property name="title">
       <string>CBM01-2</string>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
      <layout class="QVBoxLayout" name="cbm01_2_Layout">
       <item>
        <layout class="QHBoxLayout" name="cbm01_2_StatusLayout">
         <item>
          <widget class="QLabel" name="LB_Cbm01_2_StatusLed">
           <property name="text">
            <string></string>
           </property>
           <property name="statusLed" stdset="0">
            <string>unknown</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="LB_Cbm01_2_Status">
           <property name="text">
            <string>离线</string>
           </property>
           <property name="status" stdset="0">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QLabel" name="LB_Cbm01_2_Version">
         <property name="text">
          <string>固件: V1.0.0</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
         <property name="version" stdset="0">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="LB_Cbm01_2_Voltage">
         <property name="text">
          <string>5V: 0.0V</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
      </layout>
     </widget>

     <!-- CBM01-2连接线 -->
     <widget class="QFrame" name="Cbm01_2_to_CAN2">
      <property name="geometry">
       <rect>
        <x>1125</x>
        <y>508</y>
        <width>8</width>
        <height>42</height>
       </rect>
      </property>
      <property name="frameShape">
       <enum>QFrame::Box</enum>
      </property>
      <property name="busLine" stdset="0">
       <string>can</string>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
     </widget>

     <!-- 第二行设备 - CBM02-1 -->
     <widget class="QGroupBox" name="GB_Cbm02_1">
      <property name="geometry">
       <rect>
        <x>200</x>
        <y>100</y>
        <width>150</width>
        <height>120</height>
       </rect>
      </property>
      <property name="title">
       <string>CBM02-1</string>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
      <layout class="QVBoxLayout" name="cbm02_1_Layout">
       <item>
        <layout class="QHBoxLayout" name="cbm02_1_StatusLayout">
         <item>
          <widget class="QLabel" name="LB_Cbm02_1_StatusLed">
           <property name="text">
            <string></string>
           </property>
           <property name="statusLed" stdset="0">
            <string>unknown</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="LB_Cbm02_1_Status">
           <property name="text">
            <string>离线</string>
           </property>
           <property name="status" stdset="0">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QLabel" name="LB_Cbm02_1_Version">
         <property name="text">
          <string>固件: V1.0.0</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
         <property name="version" stdset="0">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="LB_Cbm02_1_Voltage">
         <property name="text">
          <string>5V: 0.0V</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
      </layout>
     </widget>

     <!-- CBM02-1连接线到CAN总线 -->
     <widget class="QFrame" name="Cbm02_1_to_CAN2_V">
      <property name="geometry">
       <rect>
        <x>275</x>
        <y>220</y>
        <width>8</width>
        <height>280</height>
       </rect>
      </property>
      <property name="frameShape">
       <enum>QFrame::Box</enum>
      </property>
      <property name="busLine" stdset="0">
       <string>can</string>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
     </widget>

     <!-- CBM02-2设备 -->
     <widget class="QGroupBox" name="GB_Cbm02_2">
      <property name="geometry">
       <rect>
        <x>1050</x>
        <y>100</y>
        <width>150</width>
        <height>120</height>
       </rect>
      </property>
      <property name="title">
       <string>CBM02-2</string>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
      <layout class="QVBoxLayout" name="cbm02_2_Layout">
       <item>
        <layout class="QHBoxLayout" name="cbm02_2_StatusLayout">
         <item>
          <widget class="QLabel" name="LB_Cbm02_2_StatusLed">
           <property name="text">
            <string></string>
           </property>
           <property name="statusLed" stdset="0">
            <string>unknown</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="LB_Cbm02_2_Status">
           <property name="text">
            <string>离线</string>
           </property>
           <property name="status" stdset="0">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QLabel" name="LB_Cbm02_2_Version">
         <property name="text">
          <string>固件: V1.0.0</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
         <property name="version" stdset="0">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="LB_Cbm02_2_Voltage">
         <property name="text">
          <string>5V: 0.0V</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
      </layout>
     </widget>

     <!-- CBM02-2连接线到CAN总线 -->
     <widget class="QFrame" name="Cbm02_2_to_CAN2_V">
      <property name="geometry">
       <rect>
        <x>1125</x>
        <y>220</y>
        <width>8</width>
        <height>280</height>
       </rect>
      </property>
      <property name="frameShape">
       <enum>QFrame::Box</enum>
      </property>
      <property name="busLine" stdset="0">
       <string>can</string>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
     </widget>

     <!-- 状态信息面板 -->
     <widget class="QGroupBox" name="GB_StatusInfo">
      <property name="geometry">
       <rect>
        <x>50</x>
        <y>50</y>
        <width>120</width>
        <height>150</height>
       </rect>
      </property>
      <property name="title">
       <string>状态说明</string>
      </property>
      <layout class="QVBoxLayout" name="statusInfoLayout">
       <item>
        <layout class="QHBoxLayout" name="onlineStatusLayout">
         <item>
          <widget class="QLabel" name="LB_OnlineIndicator">
           <property name="text">
            <string></string>
           </property>
           <property name="statusLed" stdset="0">
            <string>online</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="LB_OnlineText">
           <property name="text">
            <string>在线</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="offlineStatusLayout">
         <item>
          <widget class="QLabel" name="LB_OfflineIndicator">
           <property name="text">
            <string></string>
           </property>
           <property name="statusLed" stdset="0">
            <string>offline</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="LB_OfflineText">
           <property name="text">
            <string>离线</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="unknownStatusLayout">
         <item>
          <widget class="QLabel" name="LB_UnknownIndicator">
           <property name="text">
            <string></string>
           </property>
           <property name="statusLed" stdset="0">
            <string>unknown</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="LB_UnknownText">
           <property name="text">
            <string>未知</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>

    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
