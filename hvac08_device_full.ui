<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Hvac08DeviceFull</class>
 <widget class="QWidget" name="Hvac08DeviceFull">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1400</width>
    <height>800</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>HVAC08设备拓扑图</string>
  </property>
  <property name="styleSheet">
   <string notr="true">
/* 基础样式 */
QWidget {
    background-color: #f5f5f5;
    font-family: "Microsoft YaHei", "宋体";
    font-size: 12px;
}

/* 标题样式 */
QLabel#titleLabel {
    font-size: 18px;
    font-weight: bold;
    color: #2c3e50;
    background-color: #ecf0f1;
    border: 2px solid #bdc3c7;
    border-radius: 8px;
    padding: 10px;
    margin: 5px;
}

/* 设备组框样式 */
QGroupBox {
    border: 3px solid #95a5a6;
    border-radius: 8px;
    margin-top: 15px;
    padding-top: 10px;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:1 #ecf0f1);
    font-weight: bold;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 0 8px;
    color: #2c3e50;
    font-size: 14px;
}

/* 在线设备样式 */
QGroupBox[online="1"] {
    border: 3px solid #27ae60;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #d5f4e6, stop:1 #a8e6cf);
}

/* 离线设备样式 */
QGroupBox[online="0"] {
    border: 3px solid #e74c3c;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #fadbd8, stop:1 #f1948a);
}

/* 主控板特殊样式 */
QGroupBox#GB_Hvac08 {
    border: 4px solid #3498db;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ebf3fd, stop:1 #d6eaf8);
}

QGroupBox#GB_Hvac08[online="1"] {
    border: 4px solid #2980b9;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #d6eaf8, stop:1 #aed6f1);
}

/* IO板样式 */
QGroupBox#GB_Hvac08_IO {
    border: 2px dashed #7f8c8d;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f8f9fa, stop:1 #e9ecef);
}

/* 设备标签样式 */
QLabel {
    color: #2c3e50;
    font-size: 12px;
}

QLabel[deviceName="true"] {
    font-size: 14px;
    font-weight: bold;
    color: #2c3e50;
}

QLabel[version="true"] {
    font-size: 11px;
    color: #7f8c8d;
}

QLabel[status="true"] {
    font-size: 11px;
    color: #27ae60;
}

/* 状态指示灯样式 */
QLabel[statusLed="online"] {
    min-width: 16px;
    max-width: 16px;
    min-height: 16px;
    max-height: 16px;
    border-radius: 8px;
    background-color: #27ae60;
    border: 2px solid #1e8449;
}

QLabel[statusLed="offline"] {
    min-width: 16px;
    max-width: 16px;
    min-height: 16px;
    max-height: 16px;
    border-radius: 8px;
    background-color: #e74c3c;
    border: 2px solid #c0392b;
}

QLabel[statusLed="unknown"] {
    min-width: 16px;
    max-width: 16px;
    min-height: 16px;
    max-height: 16px;
    border-radius: 8px;
    background-color: #95a5a6;
    border: 2px solid #7f8c8d;
}

/* 连接线样式 */
QFrame[busLine="can"] {
    background-color: #3498db;
    border: 2px solid #2980b9;
}

QFrame[busLine="can"][online="1"] {
    background-color: #27ae60;
    border: 2px solid #1e8449;
}

QFrame[busLine="can"][online="0"] {
    background-color: #e74c3c;
    border: 2px solid #c0392b;
}

/* 总线标签样式 */
QLabel[busLabel="true"] {
    font-size: 13px;
    font-weight: bold;
    color: #2980b9;
    background-color: rgba(255, 255, 255, 200);
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    padding: 2px 6px;
}
   </string>
  </property>
  <layout class="QVBoxLayout" name="mainLayout">
   <item>
    <widget class="QLabel" name="titleLabel">
     <property name="text">
      <string>HVAC08设备拓扑图 - 实时状态监控</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="topologyWidget" native="true">
     <property name="minimumSize">
      <size>
       <width>1350</width>
       <height>400</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>400</height>
      </size>
     </property>

     <!-- 简化的拓扑图 -->
     <!-- 主控板 HVAC08 -->
     <widget class="QGroupBox" name="GB_Hvac08">
      <property name="geometry">
       <rect>
        <x>550</x>
        <y>150</y>
        <width>250</width>
        <height>100</height>
       </rect>
      </property>
      <property name="title">
       <string>HVAC08主控板</string>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
      <layout class="QVBoxLayout" name="hvac08Layout">
       <item>
        <widget class="QLabel" name="LB_Hvac08_Name">
         <property name="text">
          <string>扁平化控制器(HVAC08)</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
         <property name="deviceName" stdset="0">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="hvac08StatusLayout">
         <item>
          <widget class="QLabel" name="LB_Hvac08_StatusLed">
           <property name="text">
            <string></string>
           </property>
           <property name="statusLed" stdset="0">
            <string>unknown</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="LB_Hvac08_StatusText">
           <property name="text">
            <string>未知</string>
           </property>
           <property name="status" stdset="0">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="GB_Hvac08_IO">
           <property name="title">
            <string>IO板</string>
           </property>
           <property name="online" stdset="0">
            <number>0</number>
           </property>
           <layout class="QHBoxLayout" name="ioLayout">
            <item>
             <widget class="QLabel" name="LB_Hvac08_IO_StatusLed">
              <property name="text">
               <string></string>
              </property>
              <property name="statusLed" stdset="0">
               <string>unknown</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>

     <!-- CAN-2总线 -->
     <widget class="QFrame" name="CAN2_Bus_Main">
      <property name="geometry">
       <rect>
        <x>100</x>
        <y>300</y>
        <width>1150</width>
        <height>8</height>
       </rect>
      </property>
      <property name="frameShape">
       <enum>QFrame::Box</enum>
      </property>
      <property name="busLine" stdset="0">
       <string>can</string>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
     </widget>

     <!-- CAN-2总线标签 -->
     <widget class="QLabel" name="LB_CAN2_Label">
      <property name="geometry">
       <rect>
        <x>650</x>
        <y>275</y>
        <width>100</width>
        <height>25</height>
       </rect>
      </property>
      <property name="text">
       <string>CAN-2总线</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
      <property name="busLabel" stdset="0">
       <bool>true</bool>
      </property>
     </widget>

     <!-- 主控板到CAN总线的连接线 -->
     <widget class="QFrame" name="HVAC08_to_CAN2">
      <property name="geometry">
       <rect>
        <x>675</x>
        <y>250</y>
        <width>8</width>
        <height>50</height>
       </rect>
      </property>
      <property name="frameShape">
       <enum>QFrame::Box</enum>
      </property>
      <property name="busLine" stdset="0">
       <string>can</string>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
     </widget>

     <!-- 简化的设备显示 -->
     <!-- EXIO01-1设备 -->
     <widget class="QGroupBox" name="GB_Exio01_1">
      <property name="geometry">
       <rect>
        <x>150</x>
        <y>320</y>
        <width>80</width>
        <height>60</height>
       </rect>
      </property>
      <property name="title">
       <string>EXIO01-1</string>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
      <layout class="QVBoxLayout" name="exio01_1_Layout">
       <item>
        <widget class="QLabel" name="LB_Exio01_1_StatusLed">
         <property name="text">
          <string></string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
         <property name="statusLed" stdset="0">
          <string>unknown</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>

     <!-- EXIO01-1连接线 -->
     <widget class="QFrame" name="Exio01_1_to_CAN2">
      <property name="geometry">
       <rect>
        <x>190</x>
        <y>308</y>
        <width>8</width>
        <height>12</height>
       </rect>
      </property>
      <property name="frameShape">
       <enum>QFrame::Box</enum>
      </property>
      <property name="busLine" stdset="0">
       <string>can</string>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
     </widget>

     <!-- 其他设备简化显示 -->
     <!-- EXIO01-2设备 -->
     <widget class="QGroupBox" name="GB_Exio01_2">
      <property name="geometry">
       <rect>
        <x>250</x>
        <y>320</y>
        <width>80</width>
        <height>60</height>
       </rect>
      </property>
      <property name="title">
       <string>EXIO01-2</string>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
      <layout class="QVBoxLayout" name="exio01_2_Layout">
       <item>
        <widget class="QLabel" name="LB_Exio01_2_StatusLed">
         <property name="text">
          <string></string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
         <property name="statusLed" stdset="0">
          <string>unknown</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>

     <!-- EXIO02-1设备 -->
     <widget class="QGroupBox" name="GB_Exio02_1">
      <property name="geometry">
       <rect>
        <x>350</x>
        <y>320</y>
        <width>80</width>
        <height>60</height>
       </rect>
      </property>
      <property name="title">
       <string>EXIO02-1</string>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
      <layout class="QVBoxLayout" name="exio02_1_Layout">
       <item>
        <widget class="QLabel" name="LB_Exio02_1_StatusLed">
         <property name="text">
          <string></string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
         <property name="statusLed" stdset="0">
          <string>unknown</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>

     <!-- EXIO02-2设备 -->
     <widget class="QGroupBox" name="GB_Exio02_2">
      <property name="geometry">
       <rect>
        <x>450</x>
        <y>320</y>
        <width>80</width>
        <height>60</height>
       </rect>
      </property>
      <property name="title">
       <string>EXIO02-2</string>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
      <layout class="QVBoxLayout" name="exio02_2_Layout">
       <item>
        <widget class="QLabel" name="LB_Exio02_2_StatusLed">
         <property name="text">
          <string></string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
         <property name="statusLed" stdset="0">
          <string>unknown</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>

     <!-- CBM01-1设备 -->
     <widget class="QGroupBox" name="GB_Cbm01_1">
      <property name="geometry">
       <rect>
        <x>850</x>
        <y>320</y>
        <width>80</width>
        <height>60</height>
       </rect>
      </property>
      <property name="title">
       <string>CBM01-1</string>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
      <layout class="QVBoxLayout" name="cbm01_1_Layout">
       <item>
        <widget class="QLabel" name="LB_Cbm01_1_StatusLed">
         <property name="text">
          <string></string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
         <property name="statusLed" stdset="0">
          <string>unknown</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>

     <!-- CBM01-2设备 -->
     <widget class="QGroupBox" name="GB_Cbm01_2">
      <property name="geometry">
       <rect>
        <x>950</x>
        <y>320</y>
        <width>80</width>
        <height>60</height>
       </rect>
      </property>
      <property name="title">
       <string>CBM01-2</string>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
      <layout class="QVBoxLayout" name="cbm01_2_Layout">
       <item>
        <widget class="QLabel" name="LB_Cbm01_2_StatusLed">
         <property name="text">
          <string></string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
         <property name="statusLed" stdset="0">
          <string>unknown</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>

     <!-- CBM02-1设备 -->
     <widget class="QGroupBox" name="GB_Cbm02_1">
      <property name="geometry">
       <rect>
        <x>1050</x>
        <y>320</y>
        <width>80</width>
        <height>60</height>
       </rect>
      </property>
      <property name="title">
       <string>CBM02-1</string>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
      <layout class="QVBoxLayout" name="cbm02_1_Layout">
       <item>
        <widget class="QLabel" name="LB_Cbm02_1_StatusLed">
         <property name="text">
          <string></string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
         <property name="statusLed" stdset="0">
          <string>unknown</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>

     <!-- CBM02-2设备 -->
     <widget class="QGroupBox" name="GB_Cbm02_2">
      <property name="geometry">
       <rect>
        <x>1150</x>
        <y>320</y>
        <width>80</width>
        <height>60</height>
       </rect>
      </property>
      <property name="title">
       <string>CBM02-2</string>
      </property>
      <property name="online" stdset="0">
       <number>0</number>
      </property>
      <layout class="QVBoxLayout" name="cbm02_2_Layout">
       <item>
        <widget class="QLabel" name="LB_Cbm02_2_StatusLed">
         <property name="text">
          <string></string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
         <property name="statusLed" stdset="0">
          <string>unknown</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>

    </widget>
   </item>

   <!-- 详细数据显示区域 -->
   <item>
    <widget class="QTabWidget" name="dataTabWidget">
     <property name="currentIndex">
      <number>0</number>
     </property>

     <!-- HVAC08主控板数据 -->
     <widget class="QWidget" name="tab_hvac08">
      <attribute name="title">
       <string>HVAC08主控板</string>
      </attribute>
      <layout class="QGridLayout" name="hvac08DataLayout">
       <item row="0" column="0">
        <widget class="QGroupBox" name="GB_Hvac08_Status">
         <property name="title">
          <string>状态信息</string>
         </property>
         <layout class="QGridLayout" name="hvac08StatusLayout">
          <item row="0" column="0">
           <widget class="QLabel" name="label_hvac08_id">
            <property name="text">
             <string>设备ID:</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QLabel" name="LB_Hvac08_ID">
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="label_hvac08_on">
            <property name="text">
             <string>设备开启:</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QLabel" name="LB_Hvac08_On">
            <property name="text">
             <string>否</string>
            </property>
           </widget>
          </item>
          <item row="2" column="0">
           <widget class="QLabel" name="label_hvac08_nor">
            <property name="text">
             <string>正常状态:</string>
            </property>
           </widget>
          </item>
          <item row="2" column="1">
           <widget class="QLabel" name="LB_Hvac08_Nor">
            <property name="text">
             <string>否</string>
            </property>
           </widget>
          </item>
          <item row="3" column="0">
           <widget class="QLabel" name="label_hvac08_can">
            <property name="text">
             <string>CAN通信:</string>
            </property>
           </widget>
          </item>
          <item row="3" column="1">
           <widget class="QLabel" name="LB_Hvac08_CAN">
            <property name="text">
             <string>断开</string>
            </property>
           </widget>
          </item>
          <item row="4" column="0">
           <widget class="QLabel" name="label_hvac08_485_0">
            <property name="text">
             <string>RS485-0:</string>
            </property>
           </widget>
          </item>
          <item row="4" column="1">
           <widget class="QLabel" name="LB_Hvac08_485_0">
            <property name="text">
             <string>断开</string>
            </property>
           </widget>
          </item>
          <item row="5" column="0">
           <widget class="QLabel" name="label_hvac08_485_1">
            <property name="text">
             <string>RS485-1:</string>
            </property>
           </widget>
          </item>
          <item row="5" column="1">
           <widget class="QLabel" name="LB_Hvac08_485_1">
            <property name="text">
             <string>断开</string>
            </property>
           </widget>
          </item>
          <item row="6" column="0">
           <widget class="QLabel" name="label_hvac08_485_2">
            <property name="text">
             <string>RS485-2:</string>
            </property>
           </widget>
          </item>
          <item row="6" column="1">
           <widget class="QLabel" name="LB_Hvac08_485_2">
            <property name="text">
             <string>断开</string>
            </property>
           </widget>
          </item>
          <item row="7" column="0">
           <widget class="QLabel" name="label_hvac08_232">
            <property name="text">
             <string>RS232:</string>
            </property>
           </widget>
          </item>
          <item row="7" column="1">
           <widget class="QLabel" name="LB_Hvac08_232">
            <property name="text">
             <string>断开</string>
            </property>
           </widget>
          </item>
          <item row="8" column="0">
           <widget class="QLabel" name="label_hvac08_eth">
            <property name="text">
             <string>以太网:</string>
            </property>
           </widget>
          </item>
          <item row="8" column="1">
           <widget class="QLabel" name="LB_Hvac08_ETH">
            <property name="text">
             <string>断开</string>
            </property>
           </widget>
          </item>
          <item row="9" column="0">
           <widget class="QLabel" name="label_hvac08_mvb">
            <property name="text">
             <string>MVB:</string>
            </property>
           </widget>
          </item>
          <item row="9" column="1">
           <widget class="QLabel" name="LB_Hvac08_MVB">
            <property name="text">
             <string>断开</string>
            </property>
           </widget>
          </item>
          <item row="10" column="0">
           <widget class="QLabel" name="label_hvac08_ad">
            <property name="text">
             <string>AD转换:</string>
            </property>
           </widget>
          </item>
          <item row="10" column="1">
           <widget class="QLabel" name="LB_Hvac08_AD">
            <property name="text">
             <string>断开</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QGroupBox" name="GB_Hvac08_Version">
         <property name="title">
          <string>版本信息</string>
         </property>
         <layout class="QGridLayout" name="hvac08VersionLayout">
          <item row="0" column="0">
           <widget class="QLabel" name="label_hvac08_fw_ver">
            <property name="text">
             <string>固件版本:</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QLabel" name="LB_Hvac08_FW_Ver">
            <property name="text">
             <string>V1.0.0</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="label_hvac08_app_ver">
            <property name="text">
             <string>应用版本:</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QLabel" name="LB_Hvac08_App_Ver">
            <property name="text">
             <string>V1.0.0</string>
            </property>
           </widget>
          </item>
          <item row="2" column="0">
           <widget class="QLabel" name="label_hvac08_boot_ver">
            <property name="text">
             <string>引导版本:</string>
            </property>
           </widget>
          </item>
          <item row="2" column="1">
           <widget class="QLabel" name="LB_Hvac08_Boot_Ver">
            <property name="text">
             <string>V1.0.0</string>
            </property>
           </widget>
          </item>
          <item row="3" column="0">
           <widget class="QLabel" name="label_hvac08_cpld_ver">
            <property name="text">
             <string>CPLD版本:</string>
            </property>
           </widget>
          </item>
          <item row="3" column="1">
           <widget class="QLabel" name="LB_Hvac08_CPLD_Ver">
            <property name="text">
             <string>V1.0.0</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QGroupBox" name="GB_Hvac08_Power">
         <property name="title">
          <string>电源信息</string>
         </property>
         <layout class="QGridLayout" name="hvac08PowerLayout">
          <item row="0" column="0">
           <widget class="QLabel" name="label_hvac08_5v">
            <property name="text">
             <string>5V电压:</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QLabel" name="LB_Hvac08_5V">
            <property name="text">
             <string>0.0V</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="label_hvac08_24v">
            <property name="text">
             <string>24V电压:</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QLabel" name="LB_Hvac08_24V">
            <property name="text">
             <string>0.0V</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QGroupBox" name="GB_Hvac08_Network">
         <property name="title">
          <string>网络信息</string>
         </property>
         <layout class="QGridLayout" name="hvac08NetworkLayout">
          <item row="0" column="0">
           <widget class="QLabel" name="label_hvac08_ip">
            <property name="text">
             <string>IP地址:</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QLabel" name="LB_Hvac08_IP">
            <property name="text">
             <string>*************</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="label_hvac08_rtc">
            <property name="text">
             <string>RTC时间:</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QLabel" name="LB_Hvac08_RTC">
            <property name="text">
             <string>2000-01-01 00:00:00</string>
            </property>
           </widget>
          </item>
          <item row="2" column="0">
           <widget class="QLabel" name="label_hvac08_485_0_baud">
            <property name="text">
             <string>RS485-0波特率:</string>
            </property>
           </widget>
          </item>
          <item row="2" column="1">
           <widget class="QLabel" name="LB_Hvac08_485_0_Baud">
            <property name="text">
             <string>9600</string>
            </property>
           </widget>
          </item>
          <item row="3" column="0">
           <widget class="QLabel" name="label_hvac08_485_1_baud">
            <property name="text">
             <string>RS485-1波特率:</string>
            </property>
           </widget>
          </item>
          <item row="3" column="1">
           <widget class="QLabel" name="LB_Hvac08_485_1_Baud">
            <property name="text">
             <string>9600</string>
            </property>
           </widget>
          </item>
          <item row="4" column="0">
           <widget class="QLabel" name="label_hvac08_485_2_baud">
            <property name="text">
             <string>RS485-2波特率:</string>
            </property>
           </widget>
          </item>
          <item row="4" column="1">
           <widget class="QLabel" name="LB_Hvac08_485_2_Baud">
            <property name="text">
             <string>9600</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>

     <!-- HVAC08-IO板数据 -->
     <widget class="QWidget" name="tab_hvac08_io">
      <attribute name="title">
       <string>HVAC08-IO板</string>
      </attribute>
      <layout class="QGridLayout" name="hvac08IODataLayout">
       <item row="0" column="0">
        <widget class="QGroupBox" name="GB_Hvac08_IO_Status">
         <property name="title">
          <string>IO板状态信息</string>
         </property>
         <layout class="QGridLayout" name="hvac08IOStatusLayout">
          <item row="0" column="0">
           <widget class="QLabel" name="label_hvac08_io_id">
            <property name="text">
             <string>设备ID:</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QLabel" name="LB_Hvac08_IO_ID">
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="label_hvac08_io_on">
            <property name="text">
             <string>设备开启:</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QLabel" name="LB_Hvac08_IO_On">
            <property name="text">
             <string>否</string>
            </property>
           </widget>
          </item>
          <item row="2" column="0">
           <widget class="QLabel" name="label_hvac08_io_nor">
            <property name="text">
             <string>正常状态:</string>
            </property>
           </widget>
          </item>
          <item row="2" column="1">
           <widget class="QLabel" name="LB_Hvac08_IO_Nor">
            <property name="text">
             <string>否</string>
            </property>
           </widget>
          </item>
          <item row="3" column="0">
           <widget class="QLabel" name="label_hvac08_io_can">
            <property name="text">
             <string>CAN通信:</string>
            </property>
           </widget>
          </item>
          <item row="3" column="1">
           <widget class="QLabel" name="LB_Hvac08_IO_CAN">
            <property name="text">
             <string>断开</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QGroupBox" name="GB_Hvac08_IO_Version">
         <property name="title">
          <string>IO板版本信息</string>
         </property>
         <layout class="QGridLayout" name="hvac08IOVersionLayout">
          <item row="0" column="0">
           <widget class="QLabel" name="label_hvac08_io_fw_ver">
            <property name="text">
             <string>固件版本:</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QLabel" name="LB_Hvac08_IO_FW_Ver">
            <property name="text">
             <string>V1.0.0</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="label_hvac08_io_boot_ver">
            <property name="text">
             <string>引导版本:</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QLabel" name="LB_Hvac08_IO_Boot_Ver">
            <property name="text">
             <string>V1.0.0</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QGroupBox" name="GB_Hvac08_IO_Power">
         <property name="title">
          <string>IO板电源信息</string>
         </property>
         <layout class="QGridLayout" name="hvac08IOPowerLayout">
          <item row="0" column="0">
           <widget class="QLabel" name="label_hvac08_io_5v">
            <property name="text">
             <string>5V电压:</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QLabel" name="LB_Hvac08_IO_5V">
            <property name="text">
             <string>0.0V</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="label_hvac08_io_24v">
            <property name="text">
             <string>24V电压:</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QLabel" name="LB_Hvac08_IO_24V">
            <property name="text">
             <string>0.0V</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>

     <!-- EXIO设备数据 -->
     <widget class="QWidget" name="tab_exio">
      <attribute name="title">
       <string>EXIO设备</string>
      </attribute>
      <layout class="QGridLayout" name="exioDataLayout">
       <item row="0" column="0">
        <widget class="QGroupBox" name="GB_Exio01_Data">
         <property name="title">
          <string>EXIO01设备</string>
         </property>
         <layout class="QGridLayout" name="exio01DataLayout">
          <item row="0" column="0">
           <widget class="QLabel" name="label_exio01_1">
            <property name="text">
             <string>EXIO01-1:</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QLabel" name="LB_Exio01_1_ID">
            <property name="text">
             <string>ID: 0</string>
            </property>
           </widget>
          </item>
          <item row="0" column="2">
           <widget class="QLabel" name="LB_Exio01_1_Status">
            <property name="text">
             <string>离线</string>
            </property>
           </widget>
          </item>
          <item row="0" column="3">
           <widget class="QLabel" name="LB_Exio01_1_Version">
            <property name="text">
             <string>V1.0.0</string>
            </property>
           </widget>
          </item>
          <item row="0" column="4">
           <widget class="QLabel" name="LB_Exio01_1_Voltage">
            <property name="text">
             <string>5V: 0.0V</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="label_exio01_2">
            <property name="text">
             <string>EXIO01-2:</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QLabel" name="LB_Exio01_2_ID">
            <property name="text">
             <string>ID: 0</string>
            </property>
           </widget>
          </item>
          <item row="1" column="2">
           <widget class="QLabel" name="LB_Exio01_2_Status">
            <property name="text">
             <string>离线</string>
            </property>
           </widget>
          </item>
          <item row="1" column="3">
           <widget class="QLabel" name="LB_Exio01_2_Version">
            <property name="text">
             <string>V1.0.0</string>
            </property>
           </widget>
          </item>
          <item row="1" column="4">
           <widget class="QLabel" name="LB_Exio01_2_Voltage">
            <property name="text">
             <string>5V: 0.0V</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QGroupBox" name="GB_Exio02_Data">
         <property name="title">
          <string>EXIO02设备</string>
         </property>
         <layout class="QGridLayout" name="exio02DataLayout">
          <item row="0" column="0">
           <widget class="QLabel" name="label_exio02_1">
            <property name="text">
             <string>EXIO02-1:</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QLabel" name="LB_Exio02_1_ID">
            <property name="text">
             <string>ID: 0</string>
            </property>
           </widget>
          </item>
          <item row="0" column="2">
           <widget class="QLabel" name="LB_Exio02_1_Status">
            <property name="text">
             <string>离线</string>
            </property>
           </widget>
          </item>
          <item row="0" column="3">
           <widget class="QLabel" name="LB_Exio02_1_Version">
            <property name="text">
             <string>V1.0.0</string>
            </property>
           </widget>
          </item>
          <item row="0" column="4">
           <widget class="QLabel" name="LB_Exio02_1_Voltage">
            <property name="text">
             <string>5V: 0.0V</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="label_exio02_2">
            <property name="text">
             <string>EXIO02-2:</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QLabel" name="LB_Exio02_2_ID">
            <property name="text">
             <string>ID: 0</string>
            </property>
           </widget>
          </item>
          <item row="1" column="2">
           <widget class="QLabel" name="LB_Exio02_2_Status">
            <property name="text">
             <string>离线</string>
            </property>
           </widget>
          </item>
          <item row="1" column="3">
           <widget class="QLabel" name="LB_Exio02_2_Version">
            <property name="text">
             <string>V1.0.0</string>
            </property>
           </widget>
          </item>
          <item row="1" column="4">
           <widget class="QLabel" name="LB_Exio02_2_Voltage">
            <property name="text">
             <string>5V: 0.0V</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>

     <!-- CBM设备数据 -->
     <widget class="QWidget" name="tab_cbm">
      <attribute name="title">
       <string>CBM设备</string>
      </attribute>
      <layout class="QGridLayout" name="cbmDataLayout">
       <item row="0" column="0">
        <widget class="QGroupBox" name="GB_Cbm01_Data">
         <property name="title">
          <string>CBM01设备</string>
         </property>
         <layout class="QGridLayout" name="cbm01DataLayout">
          <item row="0" column="0">
           <widget class="QLabel" name="label_cbm01_1">
            <property name="text">
             <string>CBM01-1:</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QLabel" name="LB_Cbm01_1_ID">
            <property name="text">
             <string>ID: 0</string>
            </property>
           </widget>
          </item>
          <item row="0" column="2">
           <widget class="QLabel" name="LB_Cbm01_1_Status">
            <property name="text">
             <string>离线</string>
            </property>
           </widget>
          </item>
          <item row="0" column="3">
           <widget class="QLabel" name="LB_Cbm01_1_FW_Ver">
            <property name="text">
             <string>固件: V1.0.0</string>
            </property>
           </widget>
          </item>
          <item row="0" column="4">
           <widget class="QLabel" name="LB_Cbm01_1_HW_Ver">
            <property name="text">
             <string>硬件: V1.0.0</string>
            </property>
           </widget>
          </item>
          <item row="0" column="5">
           <widget class="QLabel" name="LB_Cbm01_1_Voltage">
            <property name="text">
             <string>5V: 0.0V</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="label_cbm01_1_ad1">
            <property name="text">
             <string>AD1版本:</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QLabel" name="LB_Cbm01_1_AD1_Ver">
            <property name="text">
             <string>V1.0.0</string>
            </property>
           </widget>
          </item>
          <item row="1" column="2">
           <widget class="QLabel" name="label_cbm01_1_ad2">
            <property name="text">
             <string>AD2版本:</string>
            </property>
           </widget>
          </item>
          <item row="1" column="3">
           <widget class="QLabel" name="LB_Cbm01_1_AD2_Ver">
            <property name="text">
             <string>V1.0.0</string>
            </property>
           </widget>
          </item>
          <item row="2" column="0">
           <widget class="QLabel" name="label_cbm01_2">
            <property name="text">
             <string>CBM01-2:</string>
            </property>
           </widget>
          </item>
          <item row="2" column="1">
           <widget class="QLabel" name="LB_Cbm01_2_ID">
            <property name="text">
             <string>ID: 0</string>
            </property>
           </widget>
          </item>
          <item row="2" column="2">
           <widget class="QLabel" name="LB_Cbm01_2_Status">
            <property name="text">
             <string>离线</string>
            </property>
           </widget>
          </item>
          <item row="2" column="3">
           <widget class="QLabel" name="LB_Cbm01_2_FW_Ver">
            <property name="text">
             <string>固件: V1.0.0</string>
            </property>
           </widget>
          </item>
          <item row="2" column="4">
           <widget class="QLabel" name="LB_Cbm01_2_HW_Ver">
            <property name="text">
             <string>硬件: V1.0.0</string>
            </property>
           </widget>
          </item>
          <item row="2" column="5">
           <widget class="QLabel" name="LB_Cbm01_2_Voltage">
            <property name="text">
             <string>5V: 0.0V</string>
            </property>
           </widget>
          </item>
          <item row="3" column="0">
           <widget class="QLabel" name="label_cbm01_2_ad1">
            <property name="text">
             <string>AD1版本:</string>
            </property>
           </widget>
          </item>
          <item row="3" column="1">
           <widget class="QLabel" name="LB_Cbm01_2_AD1_Ver">
            <property name="text">
             <string>V1.0.0</string>
            </property>
           </widget>
          </item>
          <item row="3" column="2">
           <widget class="QLabel" name="label_cbm01_2_ad2">
            <property name="text">
             <string>AD2版本:</string>
            </property>
           </widget>
          </item>
          <item row="3" column="3">
           <widget class="QLabel" name="LB_Cbm01_2_AD2_Ver">
            <property name="text">
             <string>V1.0.0</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QGroupBox" name="GB_Cbm02_Data">
         <property name="title">
          <string>CBM02设备</string>
         </property>
         <layout class="QGridLayout" name="cbm02DataLayout">
          <item row="0" column="0">
           <widget class="QLabel" name="label_cbm02_1">
            <property name="text">
             <string>CBM02-1:</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QLabel" name="LB_Cbm02_1_ID">
            <property name="text">
             <string>ID: 0</string>
            </property>
           </widget>
          </item>
          <item row="0" column="2">
           <widget class="QLabel" name="LB_Cbm02_1_Status">
            <property name="text">
             <string>离线</string>
            </property>
           </widget>
          </item>
          <item row="0" column="3">
           <widget class="QLabel" name="LB_Cbm02_1_FW_Ver">
            <property name="text">
             <string>固件: V1.0.0</string>
            </property>
           </widget>
          </item>
          <item row="0" column="4">
           <widget class="QLabel" name="LB_Cbm02_1_HW_Ver">
            <property name="text">
             <string>硬件: V1.0.0</string>
            </property>
           </widget>
          </item>
          <item row="0" column="5">
           <widget class="QLabel" name="LB_Cbm02_1_Voltage">
            <property name="text">
             <string>5V: 0.0V</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="label_cbm02_1_ad1">
            <property name="text">
             <string>AD1版本:</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QLabel" name="LB_Cbm02_1_AD1_Ver">
            <property name="text">
             <string>V1.0.0</string>
            </property>
           </widget>
          </item>
          <item row="1" column="2">
           <widget class="QLabel" name="label_cbm02_1_ad2">
            <property name="text">
             <string>AD2版本:</string>
            </property>
           </widget>
          </item>
          <item row="1" column="3">
           <widget class="QLabel" name="LB_Cbm02_1_AD2_Ver">
            <property name="text">
             <string>V1.0.0</string>
            </property>
           </widget>
          </item>
          <item row="2" column="0">
           <widget class="QLabel" name="label_cbm02_2">
            <property name="text">
             <string>CBM02-2:</string>
            </property>
           </widget>
          </item>
          <item row="2" column="1">
           <widget class="QLabel" name="LB_Cbm02_2_ID">
            <property name="text">
             <string>ID: 0</string>
            </property>
           </widget>
          </item>
          <item row="2" column="2">
           <widget class="QLabel" name="LB_Cbm02_2_Status">
            <property name="text">
             <string>离线</string>
            </property>
           </widget>
          </item>
          <item row="2" column="3">
           <widget class="QLabel" name="LB_Cbm02_2_FW_Ver">
            <property name="text">
             <string>固件: V1.0.0</string>
            </property>
           </widget>
          </item>
          <item row="2" column="4">
           <widget class="QLabel" name="LB_Cbm02_2_HW_Ver">
            <property name="text">
             <string>硬件: V1.0.0</string>
            </property>
           </widget>
          </item>
          <item row="2" column="5">
           <widget class="QLabel" name="LB_Cbm02_2_Voltage">
            <property name="text">
             <string>5V: 0.0V</string>
            </property>
           </widget>
          </item>
          <item row="3" column="0">
           <widget class="QLabel" name="label_cbm02_2_ad1">
            <property name="text">
             <string>AD1版本:</string>
            </property>
           </widget>
          </item>
          <item row="3" column="1">
           <widget class="QLabel" name="LB_Cbm02_2_AD1_Ver">
            <property name="text">
             <string>V1.0.0</string>
            </property>
           </widget>
          </item>
          <item row="3" column="2">
           <widget class="QLabel" name="label_cbm02_2_ad2">
            <property name="text">
             <string>AD2版本:</string>
            </property>
           </widget>
          </item>
          <item row="3" column="3">
           <widget class="QLabel" name="LB_Cbm02_2_AD2_Ver">
            <property name="text">
             <string>V1.0.0</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>

     <!-- MVB设备数据 -->
     <widget class="QWidget" name="tab_mvb">
      <attribute name="title">
       <string>MVB设备</string>
      </attribute>
      <layout class="QGridLayout" name="mvbDataLayout">
       <item row="0" column="0">
        <widget class="QGroupBox" name="GB_MVB_Data">
         <property name="title">
          <string>MVB网卡信息</string>
         </property>
         <layout class="QGridLayout" name="mvbStatusLayout">
          <item row="0" column="0">
           <widget class="QLabel" name="label_mvb_id">
            <property name="text">
             <string>设备ID:</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QLabel" name="LB_MVB_ID">
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="label_mvb_on">
            <property name="text">
             <string>设备开启:</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QLabel" name="LB_MVB_On">
            <property name="text">
             <string>否</string>
            </property>
           </widget>
          </item>
          <item row="2" column="0">
           <widget class="QLabel" name="label_mvb_fw_ver">
            <property name="text">
             <string>固件版本:</string>
            </property>
           </widget>
          </item>
          <item row="2" column="1">
           <widget class="QLabel" name="LB_MVB_FW_Ver">
            <property name="text">
             <string>V1.0.0</string>
            </property>
           </widget>
          </item>
          <item row="3" column="0">
           <widget class="QLabel" name="label_mvb_app_ver">
            <property name="text">
             <string>应用版本:</string>
            </property>
           </widget>
          </item>
          <item row="3" column="1">
           <widget class="QLabel" name="LB_MVB_App_Ver">
            <property name="text">
             <string>V1.0.0</string>
            </property>
           </widget>
          </item>
          <item row="4" column="0">
           <widget class="QLabel" name="label_mvb_boot_ver">
            <property name="text">
             <string>引导版本:</string>
            </property>
           </widget>
          </item>
          <item row="4" column="1">
           <widget class="QLabel" name="LB_MVB_Boot_Ver">
            <property name="text">
             <string>V1.0.0</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>

    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
